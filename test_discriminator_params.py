#!/usr/bin/env python3
"""测试判别器参数量提升效果"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import torch
from unittest.mock import MagicMock

from src.models.gan.discriminator import TimeSeriesDiscriminator


def count_parameters(model):
    """计算模型参数量"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)


def count_branch_parameters(discriminator):
    """计算各分支参数量"""
    results = {}

    if hasattr(discriminator, 'trend_branch') and discriminator.trend_branch is not None:
        results['trend_branch'] = count_parameters(discriminator.trend_branch)

    if hasattr(discriminator, 'feature_branch') and discriminator.feature_branch is not None:
        results['feature_branch'] = count_parameters(discriminator.feature_branch)

    if hasattr(discriminator, 'temporal_branch') and discriminator.temporal_branch is not None:
        results['temporal_branch'] = count_parameters(discriminator.temporal_branch)

    # 计算注意力机制参数
    attention_params = 0
    if hasattr(discriminator, 'temporal_attention') and discriminator.temporal_attention is not None:
        attention_params += count_parameters(discriminator.temporal_attention)
    if hasattr(discriminator, 'multi_scale_attention') and discriminator.multi_scale_attention is not None:
        attention_params += count_parameters(discriminator.multi_scale_attention)
    if hasattr(discriminator, 'adaptive_attention') and discriminator.adaptive_attention is not None:
        attention_params += count_parameters(discriminator.adaptive_attention)

    results['attention_mechanisms'] = attention_params

    return results


def main():
    """主函数"""
    print("=" * 60)
    print("判别器参数量提升测试")
    print("=" * 60)

    try:
        # 使用模拟配置，避免复杂的配置加载问题
        config = MagicMock()

        # 模拟特征维度（基于日志分析，特征维度约为51）
        target_dim = 1
        condition_feature_dim = 51
        hidden_dim = 256  # 新的隐藏维度
        num_layers = 3    # 新的层数
        hidden_dims = [256, 128, 64]  # 新的隐藏维度序列

        print(f"配置信息:")
        print(f"  - 隐藏维度: {hidden_dim}")
        print(f"  - 层数: {num_layers}")
        print(f"  - 隐藏维度序列: {hidden_dims}")
        print(f"  - 目标维度: {target_dim}")
        print(f"  - 条件特征维度: {condition_feature_dim}")
        print()

        # 初始化判别器
        print("正在初始化判别器...")
        discriminator = TimeSeriesDiscriminator(
            target_dim=target_dim,
            condition_feature_dim=condition_feature_dim,
            hidden_dim=hidden_dim,
            config=config
        )

        # 计算总参数量
        total_params = count_parameters(discriminator)
        print(f"判别器总参数量: {total_params:,}")

        # 计算各分支参数量
        branch_params = count_branch_parameters(discriminator)

        print("\n各组件参数量分布:")
        print("-" * 40)

        total_branch_params = 0
        for branch_name, params in branch_params.items():
            if params > 0:
                percentage = (params / total_params) * 100
                print(f"  {branch_name:20}: {params:8,} ({percentage:5.1f}%)")
                total_branch_params += params

        # 计算其他组件参数量
        other_params = total_params - total_branch_params
        if other_params > 0:
            percentage = (other_params / total_params) * 100
            print(f"  {'其他组件':20}: {other_params:8,} ({percentage:5.1f}%)")

        print("-" * 40)
        print(f"  {'总计':20}: {total_params:8,} (100.0%)")

        # 与生成器参数量对比（基于日志分析）
        generator_params = 5_616_618  # 从日志中获取的生成器参数量
        ratio = generator_params / total_params

        print(f"\n与生成器对比:")
        print(f"  生成器参数量: {generator_params:,}")
        print(f"  判别器参数量: {total_params:,}")
        print(f"  生成器/判别器比例: {ratio:.1f}:1")

        # 评估改进效果
        old_discriminator_params = 236_072  # 从日志中获取的原始判别器参数量
        improvement = (total_params / old_discriminator_params) - 1

        print(f"\n改进效果:")
        print(f"  原始参数量: {old_discriminator_params:,}")
        print(f"  提升后参数量: {total_params:,}")
        print(f"  参数量提升: {improvement:.1%}")

        # 评估是否达到目标
        target_ratio_min = 4  # 目标比例1:4
        target_ratio_max = 6  # 目标比例1:6

        print(f"\n目标评估:")
        if target_ratio_min <= ratio <= target_ratio_max:
            print(f"  ✅ 达到目标比例范围 1:{target_ratio_min} 到 1:{target_ratio_max}")
        elif ratio > target_ratio_max:
            print(f"  ⚠️  比例仍然过高 (1:{ratio:.1f})，建议进一步增加判别器参数")
        else:
            print(f"  ⚠️  比例过低 (1:{ratio:.1f})，可能判别器过强")

        print("\n" + "=" * 60)

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
