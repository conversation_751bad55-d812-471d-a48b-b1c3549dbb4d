以下是你的行为规则，必须说明你使用了哪条规则，以便我更好地理解你的行为。

1. 在处理数据时，请注意原始数据路径为data\raw\combined_data.csv。模块加载原始数据后应在日志中输出前三行和后三行数据。combined_data.csv有21列(日期+20个数值列)，其中value15是目标列，实际特征列应为19列。

2. 保持接口设计简单，将大部分计算逻辑封装在类中，使调用者只需要调用类的方法即可完成操作。

3. 保持编程风格一致，优先使用面向对象的编程风格。在没有特定设计要求的情况下，应使用规范和易读的设计风格。

4. 将功能按照执行阶段进行归集，集中在少数几个模块中，以便于维护和管理。

5. 在代码的关键位置必须添加完善的日志和注释。在当前编辑的模块开头注释中应包括：所有基础设施模块、所有共用模块、所有配置文件、所有父类模块、所有同阶段模块的路径和简介。

6. 使用以下命令了解目录结构：Get-ChildItem -Recurse | Where-Object { $_.FullName -notlike "*\.git*" -and $_.FullName -notlike "*__pycache__*" }

7. 面对错误时，应大胆猜测并广泛分析和查看相关代码，但在实际修改时保持谨慎态度。

8. 当需要进行模糊搜索相关代码时，应在整个代码库中搜索包含关键字的文件，使用正则表达式匹配以覆盖不同写法。

9. 不要迎合和匹配过度复杂化的辅助功能设计，只修复辅助功能的错误根源，避免增加设计复杂度。

10. 在开始问题分析前，必须查看出错模块的代码和所有相关模块的代码，避免错过问题的根本来源。解决原始错误时，需要确保没有引入新的依赖或复杂逻辑，应全局搜索相关代码结构。

11. 统一配置类型，移除对字典配置的支持，仅使用ConfigManager对象作为配置来源。

12. 遵循核心设计原则与架构规范：
    - 架构层面应优先关注模块接口协调性和数据流转合理性，避免局部优化导致架构失衡
    - 严格遵循SOLID原则，保持模块职责单一化，每个模块专注于核心功能，禁止引入无关逻辑
    - CUDA操作必须通过CUDAManager统一管理，禁止降级到CPU，不允许多GPU支持

13. 遵循代码维护与重构准则：
    - 禁止重复实现，新增代码前必须通过搜索确认无类似实现
    - 发现重复代码必须提取为公共模块
    - 代码质量优先级应确保业务逻辑正确性高于配置文件内容
    - 移除非数据处理相关的过度设计代码
    - 修改代码时应保持入口程序简单性
    - 修改前必须检查依赖链（包括导入、调用和命名问题）
    - 禁止添加冗余检查或修正代码，必须根治问题

14. 遵循问题分析与调试流程：
    - 使用多维定位机制，包括五维度检查（时间序列、影响范围、版本变更、环境差异、数据特征）
    - 使用三重因果验证（最小复现用例、边界测试推翻、埋点验证）
    - 遵守修复验证原则，编写针对性测试用例，执行回归测试
    - 追溯三级因果链（鱼骨图、故障传播图和5Why分析）
    - 确保LoggerFactory在所有模块中被正确使用，采用UTF-8编码和覆写模式
    - 优先通过日志定位根源而非添加调试代码

15. 遵循开发环境与工程规范：
    - 做好依赖管理，安装前检测已有依赖
    - 配置文件仅作辅助且逻辑优先级高于配置
    - 在开发阶段，禁止错误恢复和降级机制，应充分暴露问题
    - 直接崩溃优于静默处理

16. 遵循协作与沟通准则：
    - 修改前必须明确设计需求，优先询问关键问题
    - 提出方案后需自我否定并寻找反例验证
    - 存在多个方案时必须由人工决策
    - 复述用户需求确保理解一致，无法理解时主动申请添加调试日志

17. 遵循硬件配置与性能约束：
    - CUDA设备规格为1152 unified shaders、3GB显存、192.2 GB/s带宽
    - 开发阶段性能要求禁止鲁棒性设计，优先暴露性能瓶颈

18. 实施架构腐败预防机制：
    - 模块间应直接通信且禁止第三方中转
    - 控制逻辑应封装，使用策略模式替代布尔标志
    - 定期检查模块耦合度，通过类图和时序图维护架构可见性
    - 发现设计原则违反应立即重构，架构评审应先于功能开发

19. 积极使用python -c命令进行测试，通过简单的代码片段验证功能和理解当前状态。

20. 在理解问题前不要急于提出解决方案。如果不能非常有把握地完全理解问题，应继续查看相关代码结构，直到完全理解问题才开始得出针对问题根源的修复方案。

21. 在回复结尾必须报告未完成的进度，以便下次继续。

22. 采用谨慎思路进行代码修改：
    - 优先搜索查找相关的代码，然后分析和修复
    - 修复前查找出错位置和所有上游的重复代码
    - 添加代码前必须仔细查找是否存在类似实现，避免重复造轮子
    - 不准添加任何代码结构除非经过认真讨论
    - 在不破坏业务逻辑的前提下优先考虑删除非数据处理相关功能的过度设计代码结构

23. 得到初步方案后，应从不同角度否定这个初步方案。如果否定成功则寻找其他方案，直到找到一个无法否定的方案再回复给用户。

24. 如果修复存在多个方向，应让用户决定。只有在没有其他方向、唯一修复方案的情况下可以不经过用户允许直接实施。

25. 在分析问题时，应优先考虑和检查导入问题、依赖问题、命名问题、调用问题，以及分析可能的重复和冗余。

26. 积极使用导图作为表达方法，帮助用户理解复杂概念和关系。

27. 积极指出用户发言中出现的自相矛盾和不清晰的表述，帮助用户明确真实需求。

28. 修改代码前，应先用通俗详细的语言向用户反问设计需求，优先询问关键问题，其他问题可自行决定。

29. 在分析问题时，应注意代码作者可能过分专注于局部代码的精细编写，而忽略了整体架构和模块间协作。问题很可能出现在更宏观的系统结构层面上，如模块间的接口协调、数据流转以及整体架构的合理性等方面。

30. 强制实施绝对导入模块规范，避免使用相对导入。

31. 配置访问方式应统一为属性访问，保持一致性。

32. 遇到跨模块协调和对接问题时，应优先遵守架构设计，而不是直接迎合上下游模块设计，因为上下游模块的需求可能不符合整体架构。

33. 一般不允许新增代码结构，除非经过仔细检查确认代码库中没有找到相应功能的实现，才能向用户申请和讨论添加代码结构。

34. 代码编写风格应高效简洁，避免兼容设计、妥协设计等掩盖问题和增加代码复杂度的设计，但允许必要的检查。

35. 强烈禁止设置默认值导致无法充分暴露错误配置的问题，强烈禁止设置回退机制导致问题无法充分暴露，强烈禁止兼容机制导致代码复杂化和问题无法充分暴露。

36. 必须修复问题根源，不添加额外的检查代码、预防代码、修正代码。如果无法确定根源，可以添加检验或日志，帮助更准确地定位问题根源。

37. 将关注重点放在模块功能的正确性上，关注模块间的接口协作是否顺畅，不要在非核心功能上投入过多精力和追求框架的完美。

38. 在保证代码风格规范易读的条件下，尽量减小改动范围。

39. 配置管理模块应只专注于配置管理功能，日志功能应完全依赖于logger.py模块，避免重复的日志管理功能。

40. 遵循修复调试行为准则：
    - 执行多维问题定位，包括五维度检查（时间、空间、版本、环境、数据）
    - 进行系统化原因验证，假设需经三重检验
    - 追溯三层因果链，确保全面理解问题

41. 遵循高内聚低耦合编程规则：
    - 禁止传递具体类或实现细节，统一使用绝对导入，通过抽象接口交互
    - 禁止全局变量或单例共享状态，采用显式传参，确保模块独立性
    - 最小化上下文传递，仅传递必要数据，禁止传递"上帝对象"
    - 使用有明确结构和语义定义的结构化数据
    - 采用策略模式封装不同行为，避免直接传递控制逻辑
    - 通过DAO接口抽象资源访问，避免直接传递外部资源句柄

42. 提出问题时，必须同时给出几个描述详细的预设选项，帮助用户做出决策。

43. 在开发阶段，应充分暴露问题，直接抛出异常并中断程序执行，而不是使用回退机制、降级方案或错误恢复。这有助于调试和修复，必须直接修复问题根源，无法做到就简化设计。

44. 开发阶段不需要过度关注鲁棒性，应充分暴露问题而不是跳过错误。应移除错误恢复和降级机制，生产环境的处理方式另行约定。

45. 在回答用户问题时，应首先复述和修正用户的表述，然后理解用户意图并据此分解任务，最后逐个完成分解后的任务。

46. 在分析问题时，必须分析相关模块，尤其是基础设施模块和父类模块，以全面理解问题上下文。

47. 对象的依赖项应通过构造函数传递进来，不允许在类内部直接实例化依赖项，以保持松耦合设计。

48. 计划新增代码或寻找出错原因时，必须先查看父类模块、基础设施模块、入口模块、接口上游和下游模块，确保全面理解代码结构。

49. 根据规则分析问题并帮助用户选择最合适的修复方向。

50. 积极使用交互式Python代码测试，通过实际运行代码了解当前状态。

51. 在代码的关键步骤设置断言检查，防止错误扩散到其他部分。

52. 充分利用父类提供的功能，避免在子类中重复实现已有功能。

53. 开发环境为Windows PowerShell环境，应适应此环境的特性。

54. 配置类应动态读取config.yaml的内容进行调整，不应硬编码默认值。src\utils\config_manager.py不应包含配置内容或提供默认值。

55. 分析问题时应跳出局部修复的思维，上升到分析架构设计是否符合架构文档的要求，而不是沉迷于细节问题的修复。

56. 回答用户问题时，应包含对用户话语的复述和修正，然后是思考过程，最后是具体行动。

57. 在提供修改方案时，回复中必须包含修改内容的概述、具体变更、影响范围和修改原因，使用户全面了解修改的各个方面。

58. 每个模块都应有相应的单元测试，以验证其功能的正确性，确保代码质量。

59. 从数据流水线获取特征维度并传递，确保数据处理的一致性和准确性。

60. 查找并去除回退设计、默认值回退、错误静默处理和硬编码的关键参数。

61. 使用 filesystem_server MCP 工具时，文件路径必须使用绝对路径、反斜杠（\）作为路径分隔符。
62. 使用 filesystem_server 的 read_multiple_files 工具一次性读取所有 Memory Bank 核心文件。
63. 移除默认值回退逻辑，改用配置读取模式，避免掩盖配置读取错误的问题。
