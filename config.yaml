model:
  activation: leaky_relu
  dimensions:
    base_dim: 144  # 从160调整到144，与生成器隐藏维度保持一致
  sequence_strategy:
    padding: same
    stride: 8
    type: sliding_window
    window_size: 48
  discriminator:
    hidden_dim: 192  # 适度调整：从256降到192，平衡判别器容量（目标比例4-6:1）
    num_layers: 3    # 从2层增加到3层，进一步提升参数量
    enable_dynamic_fusion: false
    enable_temporal_attention: true  # 启用TemporalMultiHeadWrapper
    enable_adaptive_dilation_attention: false
    enable_multiscale_convolution_attention: false
    learning_rate: 0.0002
    beta1: 0.5
    beta2: 0.999
    hidden_dims: [192, 96, 48]  # 调整为3层递减结构，保持合理的参数量
  dropout_rate: 0.1
  generator:
    num_layers: 3  # 从2增加到3，进一步提升生成器参数量
  generator_type: lstm
  hidden_dim: 144  # 从160调整到144，适度增加生成器参数量约30%
  loss:
    feature_matching_weight: 1.0
    temporal_consistency_weight: 0.1
    adversarial_weight: 1.0
  loss_type: mse
  n_heads: 4
  noise:
    dim: 128
    distribution: normal
    scale: 1.0
    seed: 42
    dtype: float32
    structured: true
    temporal_correlation: 0.5
    feature_correlation: 0.3
    noise_patterns: ["temporal", "feature", "pattern"]
  noise_dim: 128
  num_layers: 2
  type: gan
  use_batch_norm: true
  use_spectral_norm: true
  attention:
    dropout: 0.1
    num_heads: 4
    num_scales: 3
    dilation_rates: [1, 2, 4]
    multi_head_num_heads: 4
    multi_head_dropout: 0.1
    multi_scale_num_heads: 4
    multi_scale_num_scales: 3
    multi_scale_dropout: 0.1
    multi_scale_dilation_rates: [1, 2, 4]
    temporal_wrapper_dropout: 0.1
    adaptive_attention_num_scales: 3
    adaptive_attention_dropout: 0.1
    adaptive_attention_num_heads: 4
  feature_extractor:
    msfe_num_scales: 3
    msfe_dropout: 0.1
    msfe_kernel_sizes: [3, 5, 7]
    tsfe_num_layers: 2
    tsfe_dropout: 0.1
    tsfe_hidden_dim: 144  # 从160调整到144，与生成器隐藏维度保持一致
    msfe_input_dim: 144   # 从160调整到144，与生成器隐藏维度保持一致
    msfe_hidden_dim: 144  # 从160调整到144，与生成器隐藏维度保持一致
    tsfe_input_dim: 144   # 从160调整到144，与生成器隐藏维度保持一致
    tsfe_output_dim: 144  # 从160调整到144，与生成器隐藏维度保持一致

data:
  batch_size: 128
  columns:
    temporal:
    - date
  data_path: data/raw/combined_data.csv
  drop_last: false
  feature_dim: null
  feature_selection:
    enable: true
  num_workers: 0
  pin_memory: true
  prediction_horizon: 1
  preprocessing:
    cleaning:
      missing_value_strategy: median
      outlier_threshold: 3.0
      pre_window_size: 5
      post_window_size: 5
      level_threshold: 0.1
      volatility_threshold: 2.0
      trend_angle_threshold: 30.0
      invalid_value_handling:
        interpolation_window_size: 5
        local_median_window_size: 5
        max_interpolation_attempts: 3
      outlier_handling:
        rolling_window_size: 20
        rolling_median_window_size: 5
        log_details: true
    padding_mode: replicate
    preserve_dims: true
    price_jump_detection:
      enable: true
      window:
        forward_size: 5
        backward_size: 5
      thresholds:
        base_threshold: 0.95
        significance_boost: 1.1
        min_significance_score: 0.85
        min_percent_change: 0.06
        price_change: 0.1
        volatility_ratio: 2.0
        trend_angle: 30.0
      weights:
        level: 0.5
        volatility: 0.3
        trend: 0.2
      dynamic_adjustment:
        base_factor: 1.2
        length_adjustment:
          long_series_threshold: 1000
          long_series_factor: 1.1
          short_series_threshold: 100
          short_series_factor: 0.9
        volatility_adjustment:
          high_threshold: 0.2
          high_factor: 1.2
          low_threshold: 0.05
          low_factor: 0.9
        autocorr_adjustment:
          threshold: 0.7
          factor: 1.1
        trend_adjustment:
          threshold: 0.8
          factor: 1.1
        range_adjustment:
          threshold: 2.0
          factor: 1.1
      validation:
        max_jumps_percentage: 0.1
        min_jumps_allowed: 1
        min_percent_change: 0.06
    validation:
      enable: true
  shuffle: true
  stride: 8
  target: value15
  test_ratio: 0.15
  train_ratio: 0.7
  val_ratio: 0.15
  window_size: 48
  model_path: outputs/models/gan_model.pt
  sequence_strategy:
    padding: same
    stride: 8
    type: sliding_window
    window_size: 48
  load_period: '2020-01-01/2099-12-31'

evaluation:
  batch_size: 128
  max_value: 1e6
  metrics:
  - mse
  - rmse
  - mae
  min_variance: 1e-6
  prediction:
    batch_size: 128
    enabled: false
    output_dir: outputs/predictions
    save_format: csv

feature_engineering:
  layers:
    - level: 0
      generators: ["base_features"]
      keep_input_features: true
    - level: 1
      generators: ["time_series_features"]
      keep_input_features: true
    - level: 2
      generators: ["interaction"]
      keep_input_features: true
  keep_original_in_final: true
  base_features:
    enable: true
    keep_original: true
  columns:
    categorical: []
    numeric: []
    time_features:
    - date
  enable: true
  frequency_features:
    enable: false
    fft: true
    spectral_density: false
  quality_control:
    drop_duplicates: true
    drop_na: true
    enable: true
    outlier_detection:
      enable: true
      method: iqr
      threshold: 1.5
  statistical_features:
    enable: true
    correlation: true
    covariance: false
    pca:
      enable: false
      n_components: null
    rolling_stats:
      enable: true
      stats: ['mean', 'std']
      window_sizes:
      - 3
      - 7
      - 21
  time_series_features:
    diff_features:
      enable: true
      orders:
      - 1
      - 2
    enable: true
    lag_features:
      enable: true
      max_lag: 10
      step: 1
    volatility_features:
      dist: normal
      enable: true
      model_type: GARCH
      p: 1
      q: 1
      scale_threshold: 5000
    window_features:
      enable: true
      stats:
      - mean
      - min
      - max
      - median
      window_sizes:
      - 3
      - 12
      - 24
  time_preprocessing:
    features_to_extract:
      - 'year'
      - 'month'
      - 'day'
      - 'dayofweek'
      - 'dayofyear'
      - 'weekofyear'
      - 'quarter'
      - 'is_weekend'
      - 'month_sin'
      - 'month_cos'
      - 'dayofweek_sin'
      - 'dayofweek_cos'
  interaction_features:
    enable: true
    top_k: 5
    candidate_selection:
      enable: true
      methods: ['lag_corr', 'mutual_info']
      combination_logic: 'union'
      top_n_final_candidates: 10
      lag_corr:
        enable: true
        max_lag: 3
        abs_corr_threshold: 0.01
      mutual_info:
        enable: true
        mi_threshold: 0.01

feature_selection:
  enable: true
  method: "default_multi_stage"
  importance_threshold: 0.01
  lagged_corr:
    min_abs_corr: 0.1
    max_lag: 28
  noise_detection:
    low_variance_threshold: 0.01
    high_correlation_threshold: 0.95

logging:
  save_history: true
  performance_monitoring:
    cuda_tracking: true
    enable: true
    memory_tracking: true
  console:
    enabled: true
    level: DEBUG
  level: DEBUG
  date_format: '%Y-%m-%d %H:%M:%S'
  file:
    backup_count: 5
    max_size: 10485760
    path: logs/app.log
  format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  handlers:
    console:
      enabled: true
      level: DEBUG
    file:
      enabled: true
      level: DEBUG
  metrics:
  - loss
  - val_loss
  - mse
  - mae
  - rmse
  module_levels:
    src.data.FeatureSelector: DEBUG
    src.data.data_pipeline: DEBUG
    src.data.feature_channel: DEBUG
    src.data.feature_engineer: DEBUG
    src.data.preprocessing.data_cleaner: DEBUG
    src.data.standardization: DEBUG
    src.models.base: DEBUG
    src.models.gan: DEBUG
    src.utils.monitor_manager: DEBUG
    src.utils.resource_manager: DEBUG
    src.utils.config.loader: DEBUG

paths:
  checkpoint_dir: outputs/checkpoints
  data_dir: data
  logs_dir: logs
  model_dir: outputs/models
  model_files:
    discriminator: discriminator.pt
    generator: generator.pt
  raw_data: data/raw/combined_data.csv
  results_dir: outputs/results

prediction:
  batch_size: 128
  confidence_level: 0.95
  device: ${system.device}
  num_workers: 0
  return_confidence: true
  use_cache: true
  n_samples: 100

system:
  cache:
    enable_memory_cache: true
    size: 1000
  cuda:
    device_id: 0
    enable_memory_cache: true
    enabled: true
    memory_fraction: 0.95
    streams:
      adaptive:
        adjustment_step: 2
        enabled: true
        high_threshold: 70
        history_size: 2
        low_threshold: 50
        max_streams: 32
        min_streams: 4
        monitoring_interval: 1
      enable_monitoring: true
      max_streams: 32
      monitoring_interval: 1.0
  device: cuda
  memory:
    memory_limit: 0.95
    monitoring_interval: 60

training:
  batch_size: 128
  num_epochs: 100
  lambda_gp: 2.0  # 大幅降低梯度惩罚权重：从5.0降到2.0，减少60%，让真实对抗损失占主导
  use_adaptive_lambda_gp: false  # 禁用自适应梯度惩罚，使用固定值
  num_workers: 0
  dropout_rate: 0.1
  seed: 42
  save_dir: outputs/models
  optimizer:
    type: adam
    generator_lr: 0.0002
    discriminator_lr: 0.0002
    weight_decay: 0.0
    beta1: 0.5
    beta2: 0.999
    momentum: 0.9
    nesterov: false
    eps: 1e-8
  scheduler_g:
    type: cyclic
    base_lr: 0.0001
    max_lr: 0.001
    min_lr: 0.00001
    step_size_up: 2000
    step_size_down: 2000
    cycle_momentum: true
    base_momentum: 0.8
    max_momentum: 0.9
    mode_cyclic: triangular
    gamma: 1.0
    verbose: true
    factor: 0.9
    patience: 5
    threshold: 1e-4
    cooldown: 0
    monitor: val_loss
  scheduler_d:
    type: cyclic
    base_lr: 0.0001
    max_lr: 0.001
    min_lr: 0.00001
    step_size_up: 2000
    step_size_down: 2000
    cycle_momentum: true
    base_momentum: 0.8
    max_momentum: 0.9
    mode_cyclic: triangular
    gamma: 1.0
    verbose: true
    factor: 0.9
    patience: 5
    threshold: 1e-4
    cooldown: 0
    monitor: val_loss
  lr_scheduler:
    enabled: true
    factor: 0.9
    patience: 5
    min_delta: 1e-4
    monitor: val_loss
  early_stopping:
    enabled: true
    patience: 5
    min_delta: 1e-4
    monitor: val_loss
    mode: "min"
  checkpoint:
    enable_checkpointing: true
    metric_name: "val_loss"
    metric_mode: "min"
    keep_best_k: 3
    save_freq: 1
    keep_last_n: 1
    memory_optimization: false
  batch_size_optimizer:
    enabled: true
    initial_batch_size: 128
    min_batch_size: 32
    max_batch_size: 256
    memory_utilization_target: 0.8
    strategy: hybrid
    adjustment_interval: 100
    growth_factor: 1.2
    shrink_factor: 0.8
    stability_threshold: 5
    warmup_steps: 100
    oom_recovery: true
    step_size: 32
    patience: 3
    monitor: val_loss
  lr_balancer:
    enabled: true
    type: "loss_ratio"
    target_ratio: 1.0
    sensitivity: 0.1
    min_lr: 0.00001
    max_lr: 0.01
    epsilon: 0.00000001

  dynamic_batch_size: true
  gradient_explosion_threshold: 10.0
  gradient_clip_val: 1.0
  adaptive_lambda_gp:
    enabled: false           # 暂时禁用自适应机制，使用固定值测试
    base_lambda_gp: 10.0     # 恢复到WGAN-GP标准值10.0
    min_lambda_gp: 2.0       # 保持范围设置
    max_lambda_gp: 15.0      # 保持范围设置
    adaptation_rate: 0.05    # 适应速率
    warmup_steps: 100        # 预热步数
    update_interval: 10      # 更新间隔
    smoothing_factor: 0.7    # 平滑因子
    grad_norm_target: 1.0    # 目标梯度范数
    grad_norm_tolerance: 0.5 # 梯度范数容忍度
    verbose_logging: true    # 详细日志
  # n_critic: 移除，使用balance.min_n_critic作为初始值
  balance:
    lower_threshold: 0.1
    upper_threshold: 0.5
    min_n_critic: 1
    max_n_critic: 10
    min_g_steps: 1
    max_g_steps: 10
  mixed_precision:
    enabled: false
    dtype: "float16"
    init_scale: 65536.0
    growth_factor: 2.0
    backoff_factor: 0.5
    growth_interval: 2000
    cast_model_outputs: false

optimization:
  fast_mode:
    num_epochs: 1
    batch_size: 128
    log_level: INFO
  parameter_exploration:
    start_date: '2023-06-01'
    end_date: '2024-09-30'

version: 2.0.0

preprocessing:
  enable: true
  standardization:
    method: standard
    feature_wise: true
  normalization:
    method: minmax
    feature_range: [0, 1]
