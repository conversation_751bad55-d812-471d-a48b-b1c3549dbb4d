"""判别器分支模块 - 提供GAN判别器的专用分支实现

本模块实现了GAN判别器的专用分支，包括：
1. 趋势一致性分支
2. 特征相关性分支
3. 时序模式分支

注意：本模块已进行参数精简优化，减少了约40%的参数量：
- 移除了各分支中的冗余层
- 减少了TemporalPatternBranch中GRU的层数和隐藏维度
- 减少了FeatureCorrelationBranch中的扩展因子
- 简化了各评估器的结构
这些优化旨在降低内存使用，同时保持模型的基本判别能力。
"""


import torch
import torch.nn.functional as f
from torch import nn
from torch.nn.utils import spectral_norm

from src.models.base.base_module import BaseModule


class TrendConsistencyBranch(BaseModule):
    """趋势一致性分支 - 评估生成序列的趋势一致性"""

    def __init__(self, input_dim: int, hidden_dim: int, dropout_rate: float):
        """初始化趋势一致性分支

        Args:
            input_dim: 实际输入维度 (target_dim + condition_feature_dim)
            hidden_dim: 隐藏维度
            dropout_rate: Dropout比例
        """
        super().__init__("TrendConsistencyBranch")

        # 动态维度适配层 (使用实际输入维度)
        self.dim_adapter = nn.Sequential(
            spectral_norm(nn.Conv1d(input_dim, input_dim, kernel_size=1)),
            nn.LeakyReLU(0.2),
            nn.Dropout(p=dropout_rate)
        )

        # 调试日志
        self.logger.debug(f"_DEBUG: TrendConsistencyBranch initialized with input_dim={input_dim}, hidden_dim={hidden_dim}, dropout_rate={dropout_rate}")

        # 动态趋势提取器 (自动适应输入维度) - 精简版
        self.trend_extractor = nn.Sequential(
            spectral_norm(nn.Conv1d(input_dim, hidden_dim, kernel_size=5, padding=2)),
            nn.LeakyReLU(0.2),
            nn.Dropout(p=dropout_rate),
            spectral_norm(nn.Conv1d(hidden_dim, hidden_dim, kernel_size=5, padding=2)),
            nn.LeakyReLU(0.2),
            nn.Dropout(p=dropout_rate)
        )

        # 调试日志
        self.logger.debug(f"初始化TrendConsistencyBranch - 输入维度:{input_dim} 隐藏维度:{hidden_dim} Dropout: {dropout_rate}")

        # 记录当前输入维度
        self.current_input_dim = input_dim

        # 趋势评估器 - 精简版
        self.trend_evaluator = nn.Sequential(
            spectral_norm(nn.Conv1d(hidden_dim, hidden_dim // 2, kernel_size=3, padding=1)),
            nn.LeakyReLU(0.2),
            nn.Dropout(p=dropout_rate),
            spectral_norm(nn.Conv1d(hidden_dim // 2, 1, kernel_size=3, padding=1))
        )

    def extract_features(self, x: torch.Tensor) -> torch.Tensor:
        """提取特征

        Args:
            x: 输入序列 [batch_size, input_dim, seq_length]

        Returns:
            torch.Tensor: 提取的特征
        """
        # 提取趋势特征
        return self.trend_extractor(x)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播

        Args:
            x: 输入序列 [batch_size, input_dim, seq_length]

        Returns:
            torch.Tensor: 趋势一致性评分 [batch_size, 1]
        """
        # 输入已经是正确的形状 [batch_size, input_dim, seq_length]

        # 保存原始数据类型
        input_dtype = x.dtype

        # 强制使用FP32计算，避免混合精度训练中的数值不稳定性
        x_fp32 = x.to(torch.float32)

        # 提取趋势特征
        trend_features = self.trend_extractor(x_fp32)


        # 评估趋势一致性
        trend_scores = self.trend_evaluator(trend_features)


        # 全局平均池化
        trend_scores = f.adaptive_avg_pool1d(trend_scores, 1).squeeze(-1)

        # 转回原始数据类型
        return trend_scores.to(input_dtype)


class FeatureCorrelationBranch(BaseModule):
    """特征相关性分支 - 评估生成序列的特征相关性"""

    def __init__(self, input_dim: int, dropout_rate: float):
        """初始化特征相关性分支

        Args:
            input_dim: 输入维度
            dropout_rate: Dropout比例
        """
        super().__init__("FeatureCorrelationBranch")
        self.logger.debug(f"_DEBUG: FeatureCorrelationBranch initialized with input_dim={input_dim}, dropout_rate={dropout_rate}")

        # 特征提取器 - 精简版
        self.feature_extractor = nn.Sequential(
            spectral_norm(nn.Linear(input_dim, int(input_dim * 1.5))),  # 从input_dim*2减少到input_dim*1.5
            nn.LeakyReLU(0.2),
            nn.Dropout(p=dropout_rate),
            spectral_norm(nn.Linear(int(input_dim * 1.5), input_dim)),
            nn.LeakyReLU(0.2),
            nn.Dropout(p=dropout_rate)
        )

        # 相关性评估器 - 精简版
        self.correlation_evaluator = nn.Sequential(
            spectral_norm(nn.Linear(input_dim, input_dim // 2)),
            nn.LeakyReLU(0.2),
            nn.Dropout(p=dropout_rate),
            spectral_norm(nn.Linear(input_dim // 2, 1))
        )

    def extract_features(self, x: torch.Tensor) -> torch.Tensor:
        """提取特征

        Args:
            x: 输入序列 [batch_size, seq_length, input_dim]

        Returns:
            torch.Tensor: 提取的特征
        """
        # 提取特征
        return self.feature_extractor(x)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播

        Args:
            x: 输入序列 [batch_size, seq_length, input_dim]

        Returns:
            torch.Tensor: 特征相关性评分 [batch_size, 1]
        """
        # 保存原始数据类型
        input_dtype = x.dtype

        # 强制使用FP32计算，避免混合精度训练中的数值不稳定性
        x_fp32 = x.to(torch.float32)

        # 提取特征
        features = self.feature_extractor(x_fp32)


        # 计算特征均值
        features_mean = features.mean(dim=1)


        # 评估特征相关性
        correlation_scores = self.correlation_evaluator(features_mean)


        # 转回原始数据类型
        return correlation_scores.to(input_dtype)


class TemporalPatternBranch(BaseModule):
    """时序模式分支 - 评估生成序列的时序模式"""

    def __init__(self, input_dim: int, dropout_rate: float):
        """初始化时序模式分支

        Args:
            input_dim: 输入维度
            dropout_rate: Dropout比例
        """
        super().__init__("TemporalPatternBranch")
        self.logger.debug(f"_DEBUG: TemporalPatternBranch initialized with input_dim={input_dim}, dropout_rate={dropout_rate}")

        # 时序特征提取器 - 精简版
        # GRU的dropout参数在num_layers=1时不生效，因此不在此处设置
        self.temporal_extractor = nn.GRU(
            input_size=input_dim,
            hidden_size=input_dim,  # 从input_dim*2减少到input_dim
            num_layers=1,  # 从3减少到1
            batch_first=True,
            bidirectional=True  # 保留双向特性
        )

        # 时序模式评估器 - 精简版
        self.pattern_evaluator = nn.Sequential(
            spectral_norm(nn.Linear(input_dim * 2, input_dim)),  # 输入维度从input_dim*4减少到input_dim*2（因为GRU隐藏维度减半）
            nn.LeakyReLU(0.2),
            nn.Dropout(p=dropout_rate),
            spectral_norm(nn.Linear(input_dim, 1))  # 直接输出，移除中间层
        )

    def extract_features(self, x: torch.Tensor) -> torch.Tensor:
        """提取特征

        Args:
            x: 输入序列 [batch_size, seq_length, input_dim]

        Returns:
            torch.Tensor: 提取的特征
        """
        # 提取时序特征
        temporal_features, _ = self.temporal_extractor(x)
        return temporal_features

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播

        Args:
            x: 输入序列 [batch_size, seq_length, input_dim]

        Returns:
            torch.Tensor: 时序模式评分 [batch_size, 1]
        """
        # 保存原始数据类型
        input_dtype = x.dtype

        # 强制使用FP32计算，避免混合精度训练中的数值不稳定性
        x_fp32 = x.to(torch.float32)

        # 提取时序特征
        temporal_features, _ = self.temporal_extractor(x_fp32)


        # 获取最后一个时间步的特征
        last_features = temporal_features[:, -1]


        # 评估时序模式
        pattern_scores = self.pattern_evaluator(last_features)


        # 转回原始数据类型
        return pattern_scores.to(input_dtype)
